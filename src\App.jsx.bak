import styles from './index.module.scss';
import common from './common.module.scss'
function App() {
  return (
    <div className={common.contont}>
      <div className={styles.a5660BaappsCom}>
        <div className={styles.top}>
          <div className={styles.rectangle1430106780} />
          <div className={styles.frame2147229728}>
            <div className={styles.frame}>
              <div className={styles.buttonTab5660}>
                <p className={styles.home}>Home</p>
              </div>
              <div className={styles.buttonTab56602}>
                <p className={styles.home}>About</p>
              </div>
              <div className={styles.buttonTab5661}>
                <p className={styles.home2}>Apps</p>
              </div>
              <div className={styles.buttonTab5663}>
                <p className={styles.home3}>Faqs</p>
              </div>
              <div className={styles.buttonTab5662}>
                <p className={styles.home4}>Join us</p>
              </div>
            </div>
          </div>
          <div className={styles.frame2147229684} />
        </div>
        <div className={styles.rectangle1430106758}>
          <div className={styles.frame2147229703}>
            <div className={styles.autoWrapper}>
              <div className={styles.frame2147229700} />
              <div className={styles.frame2147229707} />
              <div className={styles.frame2147229705} />
            </div>
            <p className={styles.aboutUs}>About Us</p>
            <div className={styles.autoWrapper2}>
              <div className={styles.frame21472297052} />
              <div className={styles.frame2147229706} />
            </div>
            <div className={styles.autoWrapper3}>
              <div className={styles.rectangle1430106773} />
              <p className={styles.throughYearsOfTechni}>
                Through years of technical cultivation, we’ve forged deep expertise
                across domains. Partnering with clients through every development
                phase with dedicated craftsmanship, our commitment has earned acclaim
                from diverse industries. Grounded in today’s tech landscape, we
                pioneer open innovation to empower global businesses in navigating
                digital transformation, collectively building a more efficient and
                connected future.
              </p>
            </div>
          </div>
          <div className={styles.frame2147229711}>
            <div className={styles.rectangle1430106772}>
              <p className={styles.theTrustOfMyUsersIsM3}>
                <span className={styles.theTrustOfMyUsersIsM}>The&nbsp;</span>
                <span className={styles.theTrustOfMyUsersIsM2}>trust</span>
                <span className={styles.theTrustOfMyUsersIsM}>&nbsp;of&nbsp;</span>
                <span className={styles.theTrustOfMyUsersIsM2}>my users</span>
                <span className={styles.theTrustOfMyUsersIsM}>&nbsp;is my&nbsp;</span>
                <span className={styles.theTrustOfMyUsersIsM2}>greatest</span>
                <span className={styles.theTrustOfMyUsersIsM}>
                  &nbsp;achievement.
                </span>
              </p>
            </div>
            <img
              src="https://p9-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/f7e35ebcc33643bd9b9648d372fba197.png?rk3s=521bdb00&x-expires=1753937144&x-signature=T6KGZf%2F%2F0exdeiiCXEPPWa%2B9MdU%3D"
              className={styles.clipboard}
            />
          </div>
        </div>
        <div className={styles.weCan}>
          <div className={styles.autoWrapper4}>
            <p className={styles.weCanBeYour}>we can be your</p>
            <p className={styles.tOpChoice}>TOP Choice</p>
          </div>
          <img
            src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/06c38bde5c8742e3a914951cb9f612d7.png?rk3s=521bdb00&x-expires=1753937143&x-signature=td%2BVpl43d%2Fj0Ggve7FmWF8ybEfM%3D"
            className={styles.frame2}
          />
        </div>
        <p className={styles.viewOurWork}>view our work</p>
        <div className={styles.frame2147229701}>
          <div className={styles.rectangle1430106759}>
            <div className={styles.rectangle1430106765} />
            <div className={styles.rectangle1430106766} />
          </div>
          <div className={styles.rectangle1430106767} />
          <div className={styles.frame6}>
            <div className={styles.frame2147229699}>
              <img
                src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/5bfe1e31c3834e93b3b3dd4d9f71d477.png?rk3s=521bdb00&x-expires=1753937146&x-signature=hpznXTcmtk6QfqIYE6HgdP%2FgzTw%3D"
                className={styles.frame3}
              />
            </div>
            <div className={styles.frame2147229709}>
              <img
                src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/82e6f5d0f75741ccb2b2d4876a24f357.png?rk3s=521bdb00&x-expires=1753937144&x-signature=uDu7uT6ccbRBGosXTkLOV4BwT4s%3D"
                className={styles.frame4}
              />
            </div>
            <img
              src="https://p6-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/8cfba7e5f8d0407bad0f7121649f3b68.png?rk3s=521bdb00&x-expires=1753937144&x-signature=hmmJvmM4AY9JTjMQOAxp6m5FpTE%3D"
              className={styles.frame5}
            />
          </div>
          <div className={styles.rectangle1430106762}>
            <div className={styles.autoWrapper5}>
              <div className={styles.frame2147229714}>
                <p className={styles.greatWeather}>Great Weather</p>
                <p className={styles.realTimeWeatherUpdat}>
                  Real-time Weather Updates: We provide you with the latest weather
                  data to ensure that your travel plans are not affected by unexpected
                  weather changes.
                  <br />
                  <br />
                  All-day Weather Overview: From sunrise to sunset, get a
                  comprehensive understanding of all-day weather changes to help you
                  make the best daily decisions.
                  <br />
                  <br />
                  Weekly Weather Forecast: Get a 7-day weather forecast in advance to
                  plan outdoor activities or arrange your work schedule in advance.
                  <br />
                  <br />
                  Lifestyle Index Guide: Access a variety of lifestyle indexes,
                  including humidity and UV index, to help you dress appropriately and
                  make smart decisions.
                </p>
                <img
                  src="https://p9-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/aa35b08cbcfc4fa3bcb6f7312eb64af0.png?rk3s=521bdb00&x-expires=1753937143&x-signature=jUGNRRUscQCV1%2BygVokxV%2FQH%2Bbw%3D"
                  className={styles.frame2147229722}
                />
              </div>
              <p className={styles.a46}>4.6</p>
            </div>
            <div className={styles.autoWrapper6}>
              <div className={styles.buttonWeather}>
                <div className={styles.frame261}>
                  <div className={styles.ellipse48} />
                  <p className={styles.getTheApp}>Get the App</p>
                  <img
                    src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/bb5b976a66f44d1287c75119057ab792.png?rk3s=521bdb00&x-expires=1753937144&x-signature=aRpcSt%2BPSXlXgtYlBAArQW5EH9I%3D"
                    className={styles.arrowRightLongLine}
                  />
                </div>
              </div>
              <div className={styles.frame2147229712}>
                <div className={styles.ellipse7402} />
                <div className={styles.ellipse7403} />
                <div className={styles.ellipse7404} />
                <div className={styles.rectangle1430106781} />
              </div>
            </div>
          </div>
        </div>
        <div className={styles.frame2147229723}>
          <div className={styles.rectangle1430106763}>
            <div className={styles.rectangle14301067652} />
            <div className={styles.rectangle14301067662} />
            <div className={styles.rectangle14301067672} />
          </div>
          <div className={styles.frame8}>
            <img
              src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/af0e427d5b9645eca85253ddc0f9c5ed.png?rk3s=521bdb00&x-expires=1753937144&x-signature=ozJc8nUke7NaDCnGH9xhiM%2FhPOY%3D"
              className={styles.frame7}
            />
            <img
              src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/1418f235fcb8483dbccadc1b83f679a7.png?rk3s=521bdb00&x-expires=1753937144&x-signature=xS5C0qYhrmZ4J7al3kcwEJFM21I%3D"
              className={styles.frame7}
            />
            <img
              src="https://p6-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/792d99734c9d4ef9a942aef78b3f01a6.png?rk3s=521bdb00&x-expires=1753937144&x-signature=LQjjPid0KUD%2FX2uUi5uQSnDZVNg%3D"
              className={styles.frame7}
            />
          </div>
          <div className={styles.rectangle1430106764}>
            <div className={styles.frame2147229715}>
              <p className={styles.pDfQuickViewer}>PDF Quick Viewer</p>
              <p className={styles.realTimeWeatherUpdat}>
                Core functions:
                <br />
                Quick browsing: quickly open and view documents, and read various
                documents smoothly.
                <br />
                One-click search: enter keywords to quickly locate target files and
                content.
                <br />
                Image to PDF: support converting images to PDF files for easy
                reference and storage.
                <br />
                File management: files can be classified, renamed, batch processed,
                etc.
                <br />
                <br />
                Reasons for choosing PDF Quick Viewer:
                <br />
                Fully functional: meet daily PDF file processing needs.
                <br />
                Simple operation: no complicated learning required, easy to get
                started.
              </p>
            </div>
            <div className={styles.autoWrapper7}>
              <div className={styles.buttonPdf}>
                <div className={styles.frame2147229729}>
                  <div className={styles.ellipse482} />
                  <p className={styles.getTheApp2}>Get the App</p>
                  <img
                    src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/bcacfa1ff9604d78890870a0899d29a2.png?rk3s=521bdb00&x-expires=1753937144&x-signature=ydDpPbtF0j0rl%2BVKmyuejeBFacg%3D"
                    className={styles.arrowRightLongLine2}
                  />
                </div>
              </div>
              <img
                src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/5cd82560bdb143e789947dcda85a9f00.png?rk3s=521bdb00&x-expires=1753937144&x-signature=Auu0r05VPf%2FxbKEOLdS1QEwOpqU%3D"
                className={styles.frame21472297122}
              />
            </div>
          </div>
        </div>
        <div className={styles.frame2147229734}>
          <div className={styles.autoWrapper8}>
            <p className={styles.theBigQuestions}>The big questions</p>
            <div className={styles.frame2147229689}>
              <p className={styles.whatDevelopmentDomai}>
                What development domains do your services cover?
              </p>
              <img
                src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/2a36467ce98f421f9d29241dd4f7a44a.png?rk3s=521bdb00&x-expires=1753937144&x-signature=DPt8LsXRFMrVv7R2FGZIdQ2NTHY%3D"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229690}>
              <p className={styles.whatDevelopmentDomai}>
                How do you ensure development efficiency?
              </p>
              <img
                src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/2a36467ce98f421f9d29241dd4f7a44a.png?rk3s=521bdb00&x-expires=1753937144&x-signature=DPt8LsXRFMrVv7R2FGZIdQ2NTHY%3D"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229691}>
              <p className={styles.whatDevelopmentDomai}>
                How is progress communicated?
              </p>
              <img
                src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/2a36467ce98f421f9d29241dd4f7a44a.png?rk3s=521bdb00&x-expires=1753937144&x-signature=DPt8LsXRFMrVv7R2FGZIdQ2NTHY%3D"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229690}>
              <p className={styles.whatDevelopmentDomai}>
                What post-delivery support is provided?
              </p>
              <img
                src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/2a36467ce98f421f9d29241dd4f7a44a.png?rk3s=521bdb00&x-expires=1753937144&x-signature=DPt8LsXRFMrVv7R2FGZIdQ2NTHY%3D"
                className={styles.arrow}
              />
            </div>
          </div>
          <div className={styles.frame2147229688}>
            <div className={styles.frame2147229735}>
              <p className={styles.fAq}>FAQ</p>
              <div className={styles.frame2147229693} />
            </div>
            <p className={styles.selectTheQuestionOnT}>
              Select the question on the left to view the answer
            </p>
          </div>
          <img
            src="https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/ebba02e1abaa4f67a03c82945f51cea4.png?rk3s=521bdb00&x-expires=1753937143&x-signature=5db8ss7Q2VVOKMfSd94OC%2FBfguo%3D"
            className={styles.group14}
          />
        </div>
        <div className={styles.rectangle1430106768}>
          <p className={styles.letSMakeItHappenToge}>Let’s make it happen together</p>
          <p className={styles.bintangdisurga333Gma}><EMAIL></p>
        </div>
        <div className={styles.autoWrapper9}>
          <img
            src="https://p6-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/b3de61beae8a4d32af4569e7f169519f.png?rk3s=521bdb00&x-expires=1753937144&x-signature=H7HG%2BAWjlW5V3nufmVdAEAEyydg%3D"
            className={styles.vector176}
          />
          <img
            src="https://p9-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/4c36c2dcfdf846b89bc932309aabb12a.png?rk3s=521bdb00&x-expires=1753937144&x-signature=tjoO%2B%2BFxnBOtc4GdUXNo3ZzTuNE%3D"
            className={styles.cursorWithAFace}
          />
          <img
            src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/1c1a5641dc364a8f83d7220428abbcd1.png?rk3s=521bdb00&x-expires=1753937144&x-signature=5%2B4R1vxWB1cJ77xTW1IJ4%2FSGp8Y%3D"
            className={styles.smileyIcon}
          />
          <img
            src="https://p3-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/b0420a66988648cba4bf3b430d46eebd.png?rk3s=521bdb00&x-expires=1753937143&x-signature=Nq4Ir%2BaLQjWaLxc919sTcz1qEDM%3D"
            className={styles.group15}
          />
        </div>
      </div>
    </div>
  )
}

export default App
