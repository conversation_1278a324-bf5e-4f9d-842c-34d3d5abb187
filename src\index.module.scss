html {
  font-size: 1rem;
}

.a5660BaappsCom {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  background: #f2fbfb;
  width: 120rem;
  height: 566.375rem;
  overflow: hidden;
  letter-spacing: 0;
  color: #262626;
  font-family: Inter;

  .realTimeWeatherUpdat {
    flex-shrink: 0;
    align-self: stretch;
    margin: 3.875rem 0rem 0rem;
    color: #26262699;
    font-size: 1.5rem;
  }

  .top {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    overflow: visible;
    line-height: 2.4375rem;
    font-size: 2rem;
    background-image: linear-gradient(180deg, #def2ed 0%, #feffef 100%);
    position: relative;

    .frame2147229728 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2.4375rem 2.625rem;
      width: 100%;
      max-width: 114.75rem;
      height: 4.0625rem;
      overflow: hidden;
      z-index: 10;

      .frame {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: flex-end;
        border-radius: 0.75rem;
        padding: 0.5rem;
        width: 113.75rem;
        height: 4.0625rem;
        overflow: hidden;

        .home {
          flex-shrink: 0;
          min-width: 5.6875rem;
        }

        .buttonTab5660 {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          border-radius: 5.625rem;
          background: #f60080;
          padding: 1.25rem 1.5rem;
          width: 7.1875rem;
          height: 1.4375rem;
          overflow: hidden;
          color: #ffffff;
          font-weight: 500;
        }

        .buttonTab56602 {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          margin-left: 1rem;
          border-radius: 5.625rem;
          background: #ffffff;
          padding: 1.25rem 1.5rem;
          width: 7.1875rem;
          height: 1.4375rem;
          overflow: hidden;
        }

        .buttonTab5661 {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          margin-left: 1rem;
          border-radius: 5.625rem;
          background: #ffffff;
          padding: 1.25rem 1.5rem;
          width: 7.1875rem;
          height: 1.4375rem;
          overflow: hidden;

          .home2 {
            flex-shrink: 0;
            min-width: 4.875rem;
          }
        }

        .buttonTab5663 {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          margin-left: 1rem;
          border-radius: 5.625rem;
          background: #ffffff;
          padding: 1.25rem 1.5rem;
          width: 7.1875rem;
          height: 1.4375rem;
          overflow: hidden;

          .home3 {
            flex-shrink: 0;
            min-width: 4.625rem;
          }
        }

        .buttonTab5662 {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          margin-left: 1rem;
          border-radius: 5.625rem;
          background: #ffffff;
          padding: 1.25rem 1.5rem;
          width: 7.1875rem;
          height: 1.4375rem;
          overflow: hidden;

          .home4 {
            flex-shrink: 0;
            min-width: 6.75rem;
          }
        }
      }
    }
  }

  .rectangle1430106758 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 3.875rem 3.625rem 2.25rem 3.3125rem;
    font-size: 3.75rem;
    font-weight: 700;
    background-image: linear-gradient(180deg, #feffee 0%, #f2fbfb 100%);

    .frame2147229703 {
      display: flex;
      position: relative;
      align-items: flex-start;
      margin-left: 0.3125rem;
      border-radius: 1.75rem;
      background: #e2f3ed;
      width: 112.75rem;
      height: 54.9375rem;
      overflow: hidden;
      text-align: center;

      .autoWrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-top: -1.4375rem;
        margin-left: -4.3125rem;

        .frame2147229700 {
          margin-top: -1.4375rem;
          margin-left: 2.9375rem;
          border: 0.375rem solid #262626;
          border-radius: 1.75rem;
          width: 21.4375rem;
          height: 11.875rem;
          background-image: url(/images/06e88734d5bc7cf72f8a269c8d256a89.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }

        .frame2147229707 {
          margin-top: 8.25rem;
          margin-left: 9.4375rem;
          border: 0.375rem solid #262626;
          border-radius: 1.75rem;
          width: 21.4375rem;
          height: 11.875rem;
          background-image: url(/images/a9020986af686541fd72f201234f8bbf.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }

        .frame2147229705 {
          margin-top: 14.5rem;
          border: 0.375rem solid #262626;
          border-radius: 1.75rem;
          background-color: #ffffff;
          width: 21.4375rem;
          height: 9.0625rem;
          overflow: hidden;
          background-image: url(/images/9f434e31410a279c8275ab012ffa9749.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }
      }

      .aboutUs {
        margin: 5.875rem 0rem 0rem 23.375rem;
        width: 23.875rem;
        font-size: 5rem;
      }

      .autoWrapper2 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-top: -1.5rem;
        margin-left: 11.5625rem;

        .frame21472297052 {
          margin-top: -1.5rem;
          border: 0.375rem solid #262626;
          border-radius: 1.75rem;
          width: 31.0625rem;
          height: 11.5rem;
          background-image: url(/images/c60459ce38bd8628196cc6994fbbd5d3.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }

        .frame2147229706 {
          margin-top: 33rem;
          margin-left: 7.5rem;
          border: 0.375rem solid #262626;
          border-radius: 1.75rem;
          width: 21.4375rem;
          height: 11.875rem;
          background-image: url(/images/f88cf767bd8d3432f747444abc2548f2.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }
      }

      .autoWrapper3 {
        position: absolute;
        top: 15.5rem;
        left: 28.9375rem;
        width: 67.3125rem;
        height: 33rem;
        font-size: 2.5rem;

        .rectangle1430106773 {
          position: absolute;
          top: 18.125rem;
          left: 15.1875rem;
          background: #d9ffa2;
          width: 48.0625rem;
          height: 2.75rem;
        }

        .throughYearsOfTechni {
          position: absolute;
          top: 0;
          left: 0;
          width: 67.3125rem;
          text-transform: UPPERCASE;
        }
      }
    }

    .frame2147229711 {
      position: relative;
      margin-top: 4.875rem;
      border-radius: 1.75rem;
      width: 112.75rem;
      height: 8.875rem;
      overflow: hidden;
      color: #000000;

      .rectangle1430106772 {
        display: flex;
        position: absolute;
        top: 0;
        left: 0;
        align-items: center;
        background: #d9ffa2;
        padding: 2.125rem 12.75rem 2.1875rem 2.5625rem;
        height: 4.5625rem;

        .theTrustOfMyUsersIsM3 {
          opacity: 0.3;
          width: 97.4375rem;

          .theTrustOfMyUsersIsM {
            color: #262626;
          }

          .theTrustOfMyUsersIsM2 {
            color: #26262680;
          }
        }
      }

      .clipboard {
        position: absolute;
        top: 4.5625rem;
        left: 108.6875rem;
        width: 46rem;
        height: 44.875rem;
      }
    }
  }

  .weCan {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 2.625rem;
    margin-left: 3.625rem;
    border-radius: 1.75rem;
    background: #fff2d7;
    padding: 4.3125rem 4.9375rem 66.0625rem 4rem;
    width: 103.8125rem;
    font-weight: 700;
    overflow: visible;

    .autoWrapper4 {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-top: 3.125rem;

      .weCanBeYour {
        width: 44.5rem;
        font-size: 5rem;
      }

      .tOpChoice {
        margin: 2.8125rem 0rem 0rem;
        width: 63.8125rem;
        font-size: 10rem;
        text-transform: UPPERCASE;
      }
    }

    .frame2 {
      width: 29.9375rem;
      height: 28.9375rem;
      overflow: hidden;
    }

    .boundlessCard {
      position: absolute;
      bottom: 5.11rem; // 81.81px converted to rem (81.81/16)
      left: -2.3375rem; // -17.87px converted to rem (-17.87/16) - 左边框在界面左侧17.87px
      z-index: 10;
      pointer-events: none;

      .boundlessSvg {
        width: 43.25rem; // 692px converted to rem (692/16) - SVG 实际宽度
        height: 63rem; // 1008px converted to rem (1008/16) - SVG 实际高度
        transform: rotate(-2deg); // 逆时针旋转2度
        transform-origin: center;
        display: block;
      }
    }

    .precisionCard {
      position: absolute;
      bottom: 5.97625rem; // 95.62px converted to rem (95.62/16)
      left: 36.69563rem; // 587.13px converted to rem (587.13/16)
      z-index: 10;
      pointer-events: none;

      .precisionSvg {
        width: 39.5rem; // 632px converted to rem (632/16)
        height: 58.8125rem; // 941px converted to rem (941/16)
        transform: rotate(2deg); // 顺时针旋转2度
        transform-origin: center;
        display: block;
      }
    }

    .contextualCard {
      position: absolute;
      bottom: 5.11rem; // 81.81px converted to rem (81.81/16)
      left: 74.44563rem; // 1191.13px converted to rem (1191.13/16)
      z-index: 10;
      pointer-events: none;

      .contextualSvg {
        width: 39.5rem; // 632px converted to rem (632/16)
        height: 58.875rem; // 942px converted to rem (942/16)
        transform: rotate(-2deg); // 逆时针旋转2度（正值）
        transform-origin: center;
        display: block;
      }
    }
  }

  .viewOurWork {
    margin: 7.375rem 0rem 0rem 9.75rem;
    width: 101.1875rem;
    text-align: center;
    font-size: 5rem;
    font-weight: 700;
  }

  .frame2147229701 {
    position: relative;
    margin-top: 7.375rem;
    margin-left: 3.625rem;
    border-radius: 1.75rem;
    width: 112.75rem;
    height: 57.375rem;
    overflow: hidden;

    .rectangle1430106759 {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      align-items: center;
      justify-content: space-between;
      background: #e5f8ff;
      padding-right: 21.5rem;
      width: 40.6875rem;
      height: 57.375rem;

      .rectangle1430106765 {
        background: #d5f3ff;
        width: 14.5rem;
        height: 57.375rem;
      }

      .rectangle1430106766 {
        background: #cef1ff;
        width: 13.875rem;
        height: 57.375rem;
      }
    }

    .rectangle1430106767 {
      position: absolute;
      top: 0;
      left: 53.4375rem;
      background: #ace7ff;
      width: 13.875rem;
      height: 57.375rem;
    }

    .frame6 {
      display: flex;
      position: absolute;
      top: 13.0625rem;
      left: 0;
      align-items: flex-start;
      justify-content: space-between;
      width: 80.0625rem;

      .frame2147229699 {
        display: flex;
        align-items: flex-start;
        border-radius: 0.75rem 0.75rem 0rem 0rem;
        height: 44.3125rem;
        overflow: hidden;

        .frame3 {
          width: 24.8125rem;
          height: 229.9375rem;
        }
      }

      .frame2147229709 {
        display: flex;
        align-items: flex-start;
        border-radius: 0.75rem 0.75rem 0rem 0rem;
        height: 44.3125rem;
        overflow: hidden;

        .frame4 {
          width: 24.8125rem;
          height: 74.625rem;
        }
      }

      .frame5 {
        border-radius: 0.75rem 0.75rem 0rem 0rem;
        width: 24.8125rem;
        height: 44.125rem;
      }
    }

    .rectangle1430106762 {
      display: flex;
      position: absolute;
      top: 0;
      left: 62.1875rem;
      flex-direction: column;
      align-items: flex-start;
      background: #ccf1ff;
      padding: 4.875rem 3rem 1.125rem 2.125rem;

      .autoWrapper5 {
        position: relative;
        margin-left: 1.75rem;
        width: 42.8125rem;
        height: 34.875rem;
        color: #008cc2;
        font-size: 3.125rem;

        .frame2147229714 {
          display: flex;
          position: absolute;
          top: 0;
          left: 0;
          flex-direction: column;
          align-items: flex-start;
          width: 42.8125rem;

          .greatWeather {
            flex-shrink: 0;
            align-self: stretch;
            color: #262626;
            font-weight: 500;
          }

          .frame2147229722 {
            position: absolute;
            top: 1.5625rem;
            left: 32.375rem;
            flex-shrink: 0;
            margin-top: 3.875rem;
            width: 10.4375rem;
            height: 2.6875rem;
          }
        }

        .a46 {
          display: flex;
          position: absolute;
          top: -1.0625rem;
          left: 35.125rem;
          align-items: center;
          justify-content: center;
          width: 5.1875rem;
          height: 3rem;
          font-family: Moul;
        }
      }

      .autoWrapper6 {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 2.9375rem;
        width: 45.4375rem;
        line-height: 1.9375rem;
        font-size: 2rem;
        font-weight: 600;

        .buttonWeather {
          display: flex;
          align-items: flex-start;
          padding: 3.75rem 2.8125rem 4.5rem 1.625rem;
          overflow: hidden;

          .frame261 {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1.25rem;
            box-shadow: inset 0px -1px 1px 0px #00000026,
              inset 0.0625rem 0.125rem 0.0625rem 0 #ffffff, inset -0.25rem -0.5rem 0.0625rem 0 #00b8ff,
              3.4375rem 3.75rem 1.4375rem 0 #00000000, 2.1875rem 2.375rem 1.3125rem 0 #00000003,
              1.25rem 1.375rem 1.125rem 0 #0000000d, 0.5625rem 0.625rem 0.8125rem 0 #00000017,
              0.125rem 0.125rem 0.4375rem 0 #0000001a;
            background: #ffffff;
            padding: 0.375rem 1.375rem 0.8125rem 1rem;
            width: 18.5rem;
            height: 4.125rem;

            .ellipse48 {
              flex-shrink: 0;
              border-radius: 50%;
              box-shadow: inset 0px 2px 2px 0px #0000000f,
                0 -0.0625rem 0.0625rem 0 #0000001a, 0 0.125rem 0.125rem 0 #ebebea;
              background: #00b8ff;
              width: 1rem;
              height: 1rem;
            }

            .getTheApp {
              flex-shrink: 0;
              margin: 0rem 0rem 0rem 1.25rem;
              min-width: 11.625rem;
              text-shadow: 0px 2px 0px #ebebeb;
            }

            .arrowRightLongLine {
              flex-shrink: 0;
              margin-left: 1.25rem;
              width: 2.75rem;
              height: 2.75rem;
              overflow: hidden;
            }
          }
        }

        .frame2147229712 {
          position: relative;
          margin-top: 2.9375rem;
          width: 6.875rem;
          height: 6.875rem;
          overflow: hidden;

          .ellipse7402 {
            position: absolute;
            top: 2.4375rem;
            left: 3.75rem;
            border-radius: 50%;
            background: #55d0ff;
            width: 2.5625rem;
            height: 2.5625rem;
          }

          .ellipse7403 {
            position: absolute;
            top: 2.75rem;
            left: 0.5625rem;
            border-radius: 50%;
            background: #55d0ff;
            width: 2.1875rem;
            height: 2.1875rem;
          }

          .ellipse7404 {
            position: absolute;
            top: 1.5rem;
            left: 2.125rem;
            border-radius: 50%;
            background: #55d0ff;
            width: 2.5625rem;
            height: 2.5625rem;
          }

          .rectangle1430106781 {
            position: absolute;
            top: 3.4375rem;
            left: 1.8125rem;
            background: #55d0ff;
            width: 3.1875rem;
            height: 1.5625rem;
          }
        }
      }
    }
  }

  .frame2147229723 {
    position: relative;
    margin-top: 3.125rem;
    margin-left: 3.625rem;
    width: 112.75rem;
    height: 57.4375rem;
    font-size: 2rem;
    font-weight: 600;

    .rectangle1430106763 {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      align-items: center;
      justify-content: space-between;
      border-radius: 1.625rem;
      background: #f8ffee;
      padding-right: 42.875rem;
      width: 69.875rem;
      height: 57.375rem;

      .rectangle14301067652 {
        background: #e8ffc8;
        width: 14.5rem;
        height: 57.375rem;
      }

      .rectangle14301067662 {
        background: #d9ffa2;
        width: 15.875rem;
        height: 57.375rem;
      }

      .rectangle14301067672 {
        background: #c2f578;
        width: 15.875rem;
        height: 57.375rem;
      }
    }

    .frame8 {
      display: flex;
      position: absolute;
      top: 13.125rem;
      left: 0;
      align-items: center;
      justify-content: space-between;
      width: 80.0625rem;
      height: 44.3125rem;

      .frame7 {
        border-radius: 0.75rem 0.75rem 0rem 0rem;
        width: 24.8125rem;
        height: 44.3125rem;
      }
    }

    .rectangle1430106764 {
      display: flex;
      position: absolute;
      top: 0;
      left: 62.1875rem;
      flex-direction: column;
      align-items: flex-start;
      border-radius: 0rem 1.75rem 1.75rem 0rem;
      background: #d9ffa2;
      padding: 4.875rem 3rem 1.125rem 2.125rem;

      .frame2147229715 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 1.75rem;
        width: 42.8125rem;

        .pDfQuickViewer {
          flex-shrink: 0;
          align-self: stretch;
          font-size: 3.125rem;
          font-weight: 500;
        }
      }

      .autoWrapper7 {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 4.75rem;
        width: 45.4375rem;
        line-height: 1.9375rem;

        .buttonPdf {
          display: flex;
          align-items: flex-start;
          padding: 3.75rem 2.8125rem 4.5rem 1.625rem;
          overflow: hidden;

          .frame2147229729 {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1.25rem;
            box-shadow: inset 0px -1px 1px 0px #00000026,
              inset 0.0625rem 0.125rem 0.0625rem 0 #ffffff, inset -0.25rem -0.5rem 0.0625rem 0 #92e41b,
              3.4375rem 3.75rem 1.4375rem 0 #00000000, 2.1875rem 2.375rem 1.3125rem 0 #00000003,
              1.25rem 1.375rem 1.125rem 0 #0000000d, 0.5625rem 0.625rem 0.8125rem 0 #00000017,
              0.125rem 0.125rem 0.4375rem 0 #0000001a;
            background: #ffffff;
            padding: 0.375rem 1.375rem 0.8125rem 1rem;
            width: 18.5rem;
            height: 4.125rem;

            .ellipse482 {
              flex-shrink: 0;
              border-radius: 50%;
              box-shadow: inset 0px 2px 2px 0px #0000000f,
                0 -0.0625rem 0.0625rem 0 #0000001a, 0 0.125rem 0.125rem 0 #ebebea;
              background: #92e41b;
              width: 1rem;
              height: 1rem;
            }

            .getTheApp2 {
              flex-shrink: 0;
              margin: 0rem 0rem 0rem 1.0625rem;
              min-width: 11.625rem;
              text-shadow: 0px 2px 0px #ebebeb;
            }

            .arrowRightLongLine2 {
              flex-shrink: 0;
              margin-left: 1.0625rem;
              width: 2.75rem;
              height: 2.75rem;
              overflow: hidden;
            }
          }
        }

        .frame21472297122 {
          margin-top: 2.9375rem;
          width: 6.875rem;
          height: 6.875rem;
          overflow: hidden;
        }
      }
    }
  }

  .frame2147229734 {
    display: flex;
    position: relative;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 14.4375rem;
    margin-left: 3.625rem;
    padding-top: 3.3125rem;
    width: 112.75rem;

    .autoWrapper8 {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .arrow {
        flex-shrink: 0;
        margin-left: 2.5rem;
        width: 4.375rem;
        height: 4.375rem;
        overflow: hidden;
      }

      .whatDevelopmentDomai {
        flex-shrink: 0;
        width: 38.875rem;
      }

      .theBigQuestions {
        width: 45.1875rem;
        font-size: 5rem;
        font-weight: 700;
        text-transform: UPPERCASE;
      }

      .frame2147229689 {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 3.8125rem;
        border-radius: 3.5rem;
        background: #ffffff;
        padding: 3.9375rem 2.75rem;
        width: 47rem;
        height: 8.375rem;
        overflow: hidden;
        color: #26262680;
        font-size: 2.125rem;
        font-weight: 500;
      }

      .frame2147229690 {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3.5rem;
        background: #ffffff;
        padding: 4.1875rem 2.75rem;
        width: 47rem;
        height: 7.875rem;
        overflow: hidden;
        color: #26262680;
        font-size: 2.125rem;
        font-weight: 500;
      }

      .frame2147229691 {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3.5rem;
        background: #ffffff;
        padding: 4.5625rem 2.75rem;
        width: 47rem;
        height: 7.125rem;
        overflow: hidden;
        color: #26262680;
        font-size: 2.125rem;
        font-weight: 500;
      }
    }

    .frame2147229688 {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 0.125rem;
      border-radius: 3.5rem;
      background: #ffffff;
      padding: 4.75rem 8.4375rem;
      width: 43.375rem;
      height: 71.3125rem;
      overflow: hidden;
      text-align: center;

      .frame2147229735 {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        align-items: center;
        align-self: stretch;
        height: 40.3125rem;
        font-size: 6.25rem;
        font-weight: 500;

        .fAq {
          flex-shrink: 0;
          align-self: stretch;
          text-transform: UPPERCASE;
        }

        .frame2147229693 {
          flex-shrink: 0;
          margin-top: 8.6875rem;
          border: 0.375rem solid #121212;
          border-radius: 1.75rem;
          background-color: #ffffff;
          width: 21.4375rem;
          height: 23.3125rem;
          overflow: hidden;
          background-image: url(/images/83a969dda0a5a23d4d0142362d142ac2.png);
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
        }
      }

      .selectTheQuestionOnT {
        flex-shrink: 0;
        align-self: stretch;
        margin: 9.125rem 0rem 0rem;
        color: #26262680;
        font-family: "Inclusive Sans";
        font-size: 2.125rem;
      }
    }

    .group14 {
      position: absolute;
      top: 0;
      left: 32.25rem;
      width: 17.875rem;
      height: 17.875rem;
    }
  }

  .rectangle1430106768 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 34.625rem 8.25rem 8.625rem 7.5625rem;
    background-image: linear-gradient(180deg, #feffee 0%, #f2fbfb 100%);

    .letSMakeItHappenToge {
      width: 67.5rem;
      font-family: Moul;
      font-size: 5rem;
      text-transform: UPPERCASE;
    }

    .bintangdisurga333Gma {
      margin: 8.5rem 0rem 0rem 32.5625rem;
      width: 71.625rem;
      text-align: right;
      color: #2f9a9a;
      font-size: 3.75rem;
      font-weight: 700;
      text-transform: UPPERCASE;
    }
  }

  .autoWrapper9 {
    position: absolute;
    top: 501rem;
    left: -4.5625rem;
    width: 129.0625rem;
    height: 67.1875rem;

    .vector176 {
      position: absolute;
      top: 0;
      left: 0;
      width: 129.0625rem;
      height: 24.0625rem;
      rotate: -180deg;
    }

    .cursorWithAFace {
      position: absolute;
      top: 32.125rem;
      left: 90.5625rem;
      width: 24.6875rem;
      height: 16.4375rem;
      rotate: -126deg;
    }

    .smileyIcon {
      position: absolute;
      top: 52.1875rem;
      left: 11rem;
      width: 15.25rem;
      height: 15rem;
    }

    .group15 {
      position: absolute;
      top: 17.3125rem;
      left: 72.875rem;
      width: 13.1875rem;
      height: 12.75rem;
      rotate: -180deg;
    }
  }

  .boxSection {
    margin-top: 78px;
    margin-bottom: 78px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .boxSvg {
      width: 100%;
      max-width: 1804px;
      height: auto;
      display: block;
    }
  }
}