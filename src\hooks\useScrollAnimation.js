import { useEffect, useRef, useState } from 'react';
import { useInView } from 'framer-motion';

export const useScrollAnimation = (threshold = 0.1) => {
  const ref = useRef();
  const isInView = useInView(ref, {
    once: true, // 只触发一次
    margin: "0px 0px -100px 0px" // 提前触发
  });

  return [ref, isInView];
};

export const useSequentialAnimation = (isVisible, count = 3, delay = 200) => {
  const [animatedItems, setAnimatedItems] = useState(new Set());

  useEffect(() => {
    if (isVisible && animatedItems.size === 0) {
      // 依次触发动画
      for (let i = 0; i < count; i++) {
        setTimeout(() => {
          setAnimatedItems(prev => new Set([...prev, i]));
        }, i * delay);
      }
    }
  }, [isVisible, count, delay, animatedItems.size]);

  return animatedItems;
};
