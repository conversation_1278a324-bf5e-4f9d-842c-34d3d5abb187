node 版本 v20.12.0

1. npm i 安装依赖 
2. ui图转码后 将页面元素结构代码 黏贴到 App.jsx 中  <div className={common.contont}> 这里插入 </div>
3. 将样式代码黏贴到 index.module.scss
4. npm run dev 运行项目 
5. 静态资源为平台托管地址 需要自行下载替换到结构中 
例如

原：
   <img src="https://........" className={styles.arrowRightUpLine}/>


替换更改后
   import topright from "./assets/jsx/...png"
   <img src={topright} className={styles.arrowRightUpLine}/>


6. 替换项目标签页图标 替换 assets/react.svg  文件
7. 替换项目标签页名称 替换 项目文件夹下 html文件下 <title>template</title> 的 内容
8. 开发调试完后 npm run build 打包成dist文件

