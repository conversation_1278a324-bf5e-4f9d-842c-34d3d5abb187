import styles from './index.module.scss';
import common from './common.module.scss';
import HeroSection from './components/HeroSection';
import AboutUs from './components/AboutUs';
import PyramidAnimation from './components/PyramidAnimation';
import { useScrollAnimation, useSequentialAnimation } from './hooks/useScrollAnimation';
import { motion } from 'framer-motion';

function App() {
  const [cardsRef, isCardsVisible] = useScrollAnimation(0.1);
  const animatedCards = useSequentialAnimation(isCardsVisible, 3, 200);

  return (
    <div className={common.contont}>
      <div className={styles.a5660BaappsCom}>
        <div className={styles.top}>
          <div className={styles.frame2147229728}>
            <div className={styles.frame}>
              <div className={styles.buttonTab5660}>
                <p className={styles.home}>Home</p>
              </div>
              <div className={styles.buttonTab56602}>
                <p className={styles.home}>About</p>
              </div>
              <div className={styles.buttonTab5661}>
                <p className={styles.home2}>Apps</p>
              </div>
              <div className={styles.buttonTab5663}>
                <p className={styles.home3}>Faqs</p>
              </div>
              <div className={styles.buttonTab5662}>
                <p className={styles.home4}>Join us</p>
              </div>
            </div>
          </div>
          <HeroSection />
        </div>
        <AboutUs />
        <div className={styles.boxSection}>
          <img
            src="/images/box.svg"
            alt="Box section with company achievements and trust indicators"
            className={styles.boxSvg}
          />
        </div>
        <div className={styles.weCan} ref={cardsRef}>
          <div className={styles.autoWrapper4}>
            <p className={styles.weCanBeYour}>we can be your</p>
            <p className={styles.tOpChoice}>TOP Choice</p>
          </div>
          <PyramidAnimation
            className={styles.frame2}
          />
          <motion.div
            className={styles.boundlessCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(0) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/boundless.svg"
              alt="Boundless Synergy, Unified Vision - Cross-disciplinary collaboration card"
              className={styles.boundlessSvg}
            />
          </motion.div>
          <motion.div
            className={styles.precisionCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(1) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/precision.svg"
              alt="Precision Engineered, Agile Execution - Lean delivery pipelines card"
              className={styles.precisionSvg}
            />
          </motion.div>
          <motion.div
            className={styles.contextualCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(2) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/contextual.svg"
              alt="Contextual Intelligence, Targeted Resonance - Behavior modeling card"
              className={styles.contextualSvg}
            />
          </motion.div>
        </div>
        <p className={styles.viewOurWork}>view our work</p>
        <div className={styles.frame2147229701}>
          <div className={styles.rectangle1430106759}>
            <div className={styles.rectangle1430106765} />
            <div className={styles.rectangle1430106766} />
          </div>
          <div className={styles.rectangle1430106767} />
          <div className={styles.frame6}>
            <div className={styles.frame2147229699}>
              <img
                src="/images/b09bb45c5c65a57fcdb343d43da8a26e.png"
                className={styles.frame3}
              />
            </div>
            <div className={styles.frame2147229709}>
              <img
                src="/images/4717cfe379df4abbd1a04810f4cee1db.png"
                className={styles.frame4}
              />
            </div>
            <img
              src="/images/24225cc40a171732932788056f5672da.png"
              className={styles.frame5}
            />
          </div>
          <div className={styles.rectangle1430106762}>
            <div className={styles.autoWrapper5}>
              <div className={styles.frame2147229714}>
                <p className={styles.greatWeather}>Great Weather</p>
                <p className={styles.realTimeWeatherUpdat}>
                  Real-time Weather Updates: We provide you with the latest weather
                  data to ensure that your travel plans are not affected by unexpected
                  weather changes.
                  <br />
                  <br />
                  All-day Weather Overview: From sunrise to sunset, get a
                  comprehensive understanding of all-day weather changes to help you
                  make the best daily decisions.
                  <br />
                  <br />
                  Weekly Weather Forecast: Get a 7-day weather forecast in advance to
                  plan outdoor activities or arrange your work schedule in advance.
                  <br />
                  <br />
                  Lifestyle Index Guide: Access a variety of lifestyle indexes,
                  including humidity and UV index, to help you dress appropriately and
                  make smart decisions.
                </p>
                <img
                  src="/images/5782dcbf7a1beb8713bfa846d82745b3.png"
                  className={styles.frame2147229722}
                />
              </div>
              <p className={styles.a46}>4.6</p>
            </div>
            <div className={styles.autoWrapper6}>
              <div className={styles.buttonWeather}>
                <div className={styles.frame261}>
                  <div className={styles.ellipse48} />
                  <p className={styles.getTheApp}>Get the App</p>
                  <img
                    src="/images/c6764a296886ba7216434cabdfc62ab0.png"
                    className={styles.arrowRightLongLine}
                  />
                </div>
              </div>
              <div className={styles.frame2147229712}>
                <div className={styles.ellipse7402} />
                <div className={styles.ellipse7403} />
                <div className={styles.ellipse7404} />
                <div className={styles.rectangle1430106781} />
              </div>
            </div>
          </div>
        </div>
        <div className={styles.frame2147229723}>
          <div className={styles.rectangle1430106763}>
            <div className={styles.rectangle14301067652} />
            <div className={styles.rectangle14301067662} />
            <div className={styles.rectangle14301067672} />
          </div>
          <div className={styles.frame8}>
            <img
              src="/images/d1db3861023a554efabe27a7f7dc9702.png"
              className={styles.frame7}
            />
            <img
              src="/images/28b52324c4aa28e3e9e404f53ef46a3a.png"
              className={styles.frame7}
            />
            <img
              src="/images/4e53251b690408f730f03a2fd5f14fbf.png"
              className={styles.frame7}
            />
          </div>
          <div className={styles.rectangle1430106764}>
            <div className={styles.frame2147229715}>
              <p className={styles.pDfQuickViewer}>PDF Quick Viewer</p>
              <p className={styles.realTimeWeatherUpdat}>
                Core functions:
                <br />
                Quick browsing: quickly open and view documents, and read various
                documents smoothly.
                <br />
                One-click search: enter keywords to quickly locate target files and
                content.
                <br />
                Image to PDF: support converting images to PDF files for easy
                reference and storage.
                <br />
                File management: files can be classified, renamed, batch processed,
                etc.
                <br />
                <br />
                Reasons for choosing PDF Quick Viewer:
                <br />
                Fully functional: meet daily PDF file processing needs.
                <br />
                Simple operation: no complicated learning required, easy to get
                started.
              </p>
            </div>
            <div className={styles.autoWrapper7}>
              <div className={styles.buttonPdf}>
                <div className={styles.frame2147229729}>
                  <div className={styles.ellipse482} />
                  <p className={styles.getTheApp2}>Get the App</p>
                  <img
                    src="/images/e2a9c19d84d65cb53548aeb01a3a11ff.png"
                    className={styles.arrowRightLongLine2}
                  />
                </div>
              </div>
              <img
                src="/images/79ea0f0243587bbe77e03b44e01d0a6d.png"
                className={styles.frame21472297122}
              />
            </div>
          </div>
        </div>
        <div className={styles.frame2147229734}>
          <div className={styles.autoWrapper8}>
            <p className={styles.theBigQuestions}>The big questions</p>
            <div className={styles.frame2147229689}>
              <p className={styles.whatDevelopmentDomai}>
                What development domains do your services cover?
              </p>
              <img
                src="/images/c199d8d23ffe4b06e6a8f078153c03f8.png"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229690}>
              <p className={styles.whatDevelopmentDomai}>
                How do you ensure development efficiency?
              </p>
              <img
                src="/images/c199d8d23ffe4b06e6a8f078153c03f8.png"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229691}>
              <p className={styles.whatDevelopmentDomai}>
                How is progress communicated?
              </p>
              <img
                src="/images/c199d8d23ffe4b06e6a8f078153c03f8.png"
                className={styles.arrow}
              />
            </div>
            <div className={styles.frame2147229690}>
              <p className={styles.whatDevelopmentDomai}>
                What post-delivery support is provided?
              </p>
              <img
                src="/images/c199d8d23ffe4b06e6a8f078153c03f8.png"
                className={styles.arrow}
              />
            </div>
          </div>
          <div className={styles.frame2147229688}>
            <div className={styles.frame2147229735}>
              <p className={styles.fAq}>FAQ</p>
              <div className={styles.frame2147229693} />
            </div>
            <p className={styles.selectTheQuestionOnT}>
              Select the question on the left to view the answer
            </p>
          </div>
          <img
            src="/images/ca3d1cbe4be6b29726df9d1fcd826712.png"
            className={styles.group14}
          />
        </div>
        <div className={styles.rectangle1430106768}>
          <p className={styles.letSMakeItHappenToge}>Let’s make it happen together</p>
          <p className={styles.bintangdisurga333Gma}><EMAIL></p>
        </div>
        <div className={styles.autoWrapper9}>
          <img
            src="/images/996c05148287684b2e8b23d4c9918a4c.png"
            className={styles.vector176}
          />
          <img
            src="/images/6ad2bbf4dc0594c4ed37f3141981d60e.png"
            className={styles.cursorWithAFace}
          />
          <img
            src="/images/6c7ed9f764e12575dbae47326045c65e.png"
            className={styles.smileyIcon}
          />
          <img
            src="/images/2c2a412ce8407c37db3f9a7790ab3e23.png"
            className={styles.group15}
          />
        </div>
      </div>
    </div>
  );
}

export default App;
