import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styles from './CarouselComponent.module.scss';

/**
 * CarouselComponent - 无限自动轮播组件
 *
 * 实现传送带式的无限循环轮播效果，支持6张图片的自动播放
 *
 * @param {Object} props - 组件属性
 * @param {string[]} props.images - 图片路径数组
 * @param {number} props.itemWidth - 每个轮播项的宽度（rem），默认24.8125
 * @param {number} props.itemGap - 图片之间的间距（rem），默认2.8125（45px）
 * @param {number} props.speed - 轮播速度，每帧移动的rem数，默认0.5
 * @param {string} props.className - 额外的CSS类名
 * @returns {JSX.Element} 轮播组件
 */
const CarouselComponent = ({
  images = [],
  itemWidth = 24.8125,
  itemGap = 2.8125, // 45px转换为rem (45/16)
  speed = 0.5, // 轮播速度，每帧移动的rem数
  className = ''
}) => {
  const [translateX, setTranslateX] = useState(0);

  // 创建三倍图片数组以实现更平滑的无缝循环
  const tripleImages = [...images, ...images, ...images];
  const totalItems = images.length;
  const itemWidthWithGap = itemWidth + itemGap;

  useEffect(() => {
    if (images.length === 0) return;

    let animationId;

    const animate = () => {
      setTranslateX(prevX => {
        const newX = prevX - speed;
        // 当滚动完第一组图片时，重置到开始位置
        const resetPoint = -totalItems * itemWidthWithGap;
        if (newX <= resetPoint) {
          return 0;
        }
        return newX;
      });
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [images.length, totalItems, itemWidthWithGap, speed]);

  return (
    <div className={`${styles.carouselContainer} ${className}`}>
      <div className={styles.carouselWrapper}>
        <div
          className={styles.carouselTrack}
          style={{
            transform: `translateX(${translateX}rem)`,
            willChange: 'transform'
          }}
        >
          {tripleImages.map((imageSrc, index) => (
            <div
              key={`carousel-item-${index}`}
              className={styles.carouselItem}
              style={{
                width: `${itemWidth}rem`,
                marginRight: index < tripleImages.length - 1 ? `${itemGap}rem` : '0'
              }}
            >
              <img
                src={imageSrc}
                alt={`Carousel item ${(index % totalItems) + 1}`}
                className={styles.carouselImage}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CarouselComponent;
