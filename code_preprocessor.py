import os
import re
import requests
import hashlib
import urllib.parse
from pathlib import Path
import shutil
import sys
import argparse
from collections import defaultdict
from urllib.parse import quote
from bs4 import BeautifulSoup
import time
from datetime import datetime
import css_parser # 用于精确解析CSS
import logging

# --- 日志系统 ---
class Logger:
    def __init__(self, log_file=None, verbose=True):
        self.verbose = verbose
        self.log_file = log_file
        self.start_time = time.time()
        
        if self.log_file:
            Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 代码预处理工具日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
    
    def _log(self, message, color_code=None, level="INFO"):
        """内部日志方法"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_msg = f"[{timestamp}] [{level}] {message}"
        
        if self.verbose:
            if color_code and sys.stdout.isatty(): # 仅在TTY中输出颜色
                print(f"\033[{color_code}m{formatted_msg}\033[0m")
            else:
                print(formatted_msg)
        
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(formatted_msg + "\n")
    
    def info(self, message): self._log(message, "36", "INFO")
    def success(self, message): self._log(message, "32", "SUCCESS")
    def warning(self, message): self._log(message, "33", "WARNING")
    def error(self, message): self._log(message, "31", "ERROR")
    def debug(self, message): self._log(message, "35", "DEBUG")
    
    def section(self, title):
        separator = "=" * 60
        self._log(f"\n{separator}\n🚀 {title}\n{separator}", "36", "SECTION")

    def subsection(self, title):
        separator = "-" * 40
        self._log(f"\n{separator}\n📋 {title}\n{separator}", "34", "SUBSECTION")

    def get_elapsed_time(self):
        return f"{time.time() - self.start_time:.2f}秒"

# 全局日志实例
logger = None

# --- 图片下载器 ---
class ImageDownloader:
    def __init__(self, logger):
        self.logger = logger
        self.src_pattern = r'src=(["\'])(https?://[^\1> ]+\.(?:png|jpg|jpeg|gif|svg)[^ \1>]*)\1'
        self.css_url_pattern = r'url\((["\']?)(https?://[^\)\s]+\.(?:png|jpg|jpeg|gif|svg)[^\)\s]*)\1\)'
        self.download_stats = defaultdict(int)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def download_image(self, url, image_dir):
        """下载单个图片"""
        try:
            path = urllib.parse.urlparse(url).path
            ext = Path(path).suffix.lower() or '.png'
            
            url_hash = hashlib.md5(url.encode()).hexdigest()
            filename = f"{url_hash}{ext}"
            local_path = Path(image_dir) / filename
            
            if local_path.exists():
                # [优化] 日志更清晰，指明哪个URL已存在于哪个本地文件
                self.logger.info(f"图片已存在，跳过下载: {url} -> {local_path.name}")
                self.download_stats['skipped'] += 1
                return filename
            
            self.logger.info(f"正在下载: {url}")
            response = requests.get(url, stream=True, timeout=20, headers=self.headers)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.success(f"下载完成: {url} -> {filename}")
            self.download_stats['downloaded'] += 1
            return filename
        
        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载失败 {url}: {e}")
            self.download_stats['failed'] += 1
            return None

    def _get_local_path_for_url(self, url, image_dir):
        """下载图片并返回其本地路径，如果失败则返回None。"""
        self.download_stats['total_found'] += 1
        filename = self.download_image(url, image_dir)
        return f"/images/{filename}" if filename else None

    def process_content(self, content, public_images_dir):
        """处理字符串内容中的图片URL，返回处理后的内容。"""
        
        def replacer_factory(format_str):
            def replacer(match):
                quote = match.group(1)
                full_url = match.group(2)
                local_path = self._get_local_path_for_url(full_url, public_images_dir)
                if local_path:
                    return format_str.format(quote=quote, local_path=local_path)
                return match.group(0)  # 替换失败则返回原样
            return replacer

        content = re.sub(self.src_pattern, replacer_factory('src={quote}{local_path}{quote}'), content)
        content = re.sub(self.css_url_pattern, replacer_factory('url({quote}{local_path}{quote})'), content)
        
        return content

    def get_stats_summary(self):
        return (f"图片处理统计: 发现 {self.download_stats['total_found']} 个, "
                f"下载 {self.download_stats['downloaded']} 个, "
                f"跳过 {self.download_stats['skipped']} 个, "
                f"失败 {self.download_stats['failed']} 个")

# --- px转rem转换器 ---
class PxToRemConverter:
    def __init__(self, logger, base_font_size=16):
        self.logger = logger
        self.base_font_size = base_font_size
        self.conversion_stats = defaultdict(int)
        self.affected_files = set() # [优化] 记录被修改的文件
        self.px_pattern = r'(?<!\d)(?<!\.)(-?\d+(?:\.\d+)?)px(?!\w)'
        self.exclude_properties = {'border-width', 'outline-width', 'box-shadow', 'text-shadow', 'stroke-width', 'border', 'outline'}

    def px_to_rem(self, px_value):
        px_float = float(px_value)
        if px_float == 0: return '0'
        rem_value = px_float / self.base_font_size
        return f"{rem_value:.4f}".rstrip('0').rstrip('.') + "rem"

    def should_convert_property(self, css_line):
        line_no_comment = re.sub(r'/\*.*?\*/', '', css_line).strip().lower()
        for prop in self.exclude_properties:
            if line_no_comment.startswith(prop + ':'):
                self.logger.debug(f"跳过属性转换: {prop} (在行: {css_line.strip()})")
                return False, prop
        return True, None
    
    def process_content(self, content, file_path_for_logging):
        """
        处理字符串内容中的px值，返回处理后的内容。
        [优化] 核心改动点，日志输出更具体。
        """
        new_lines = []
        lines = content.splitlines(True) # 保留换行符

        for line in lines:
            should_convert, _ = self.should_convert_property(line)
            if not should_convert:
                excluded_px_count = len(re.findall(self.px_pattern, line))
                if excluded_px_count > 0:
                    self.conversion_stats['total_found'] += excluded_px_count
                    self.conversion_stats['skipped'] += excluded_px_count
                new_lines.append(line)
                continue
            
            # 使用闭包，这样replacer可以访问到外部的`line`变量
            def replacer(match):
                px_value = match.group(1)
                self.conversion_stats['total_found'] += 1
                
                # 0px 不转换，但记录为跳过
                if float(px_value) == 0:
                    self.conversion_stats['skipped'] += 1
                    # 即使是0，也记录日志，表明检查过
                    self.logger.debug(f"转换 [{line.strip()}] -> {px_value}px → 0 (跳过)")
                    return '0'
                
                rem_value = self.px_to_rem(px_value)
                self.conversion_stats['converted'] += 1
                self.affected_files.add(file_path_for_logging.name)

                # [优化] 这是您要求的核心优化，输出带上下文的日志
                self.logger.debug(f"转换 [{line.strip()}] -> {px_value}px → {rem_value}")
                return rem_value
            
            new_line = re.sub(self.px_pattern, replacer, line)
            new_lines.append(new_line)

        return "".join(new_lines)

    def print_report(self):
        self.logger.subsection("px转rem转换报告")
        self.logger.info(f"基准字体大小: {self.base_font_size}px")
        summary = (f"发现 {self.conversion_stats['total_found']} 个px值, "
                   f"转换 {self.conversion_stats['converted']} 个, "
                   f"跳过 {self.conversion_stats['skipped']} 个。")
        self.logger.info(summary)
        
        # [优化] 在报告中列出受影响的文件
        if self.affected_files:
            self.logger.info(f"共修改了 {len(self.affected_files)} 个文件:")
            for f in sorted(list(self.affected_files)):
                self.logger.info(f"  - {f}")
        else:
            self.logger.info("没有文件因px转rem而被修改。")

# --- 字体分析器 (使用css-parser重构) ---
class FontAnalyzer:
    def __init__(self, logger):
        self.logger = logger
        self.fonts = defaultdict(set)
        self.processed_files = 0
        self.generic_fonts = {'serif', 'sans-serif', 'monospace', 'cursive', 'fantasy', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Helvetica'}
        self.css_parser = css_parser.CSSParser(loglevel=logging.CRITICAL)

    def clean_font_name(self, font_name_str):
        font_name_str = re.sub(r'["\']', '', font_name_str)
        fonts = [f.strip() for f in font_name_str.split(',')]
        for font in fonts:
            if font and font not in self.generic_fonts:
                return font
        return None

    def normalize_weight(self, weight):
        weight_map = {'normal': '400', 'bold': '700', 'lighter': '300', 'bolder': '700'}
        return weight_map.get(str(weight).lower(), str(weight))

    def analyze_content(self, content, file_path_for_logging):
        """使用CSS解析器分析字符串内容中的字体。"""
        try:
            sheet = self.css_parser.parseString(content, href=file_path_for_logging)
            fonts_found_in_file = set()
            
            for rule in sheet.cssRules:
                if rule.typeString == 'STYLE_RULE':
                    style = rule.style
                    current_family = None
                    current_weight = '400'

                    font_family_prop = style.getProperty('font-family')
                    if font_family_prop:
                        clean_name = self.clean_font_name(font_family_prop.value)
                        if clean_name:
                            current_family = clean_name
                    
                    font_weight_prop = style.getProperty('font-weight')
                    if font_weight_prop:
                        current_weight = self.normalize_weight(font_weight_prop.value)
                    
                    if current_family:
                        self.fonts[current_family].add(current_weight)
                        fonts_found_in_file.add(current_family)

            if fonts_found_in_file:
                self.logger.info(f"在 {file_path_for_logging.name} 中发现字体: {', '.join(sorted(list(fonts_found_in_file)))}")
            self.processed_files += 1

        except Exception as e:
            self.logger.error(f"解析CSS文件失败 {file_path_for_logging}: {e}")
    
    def generate_google_fonts_url(self):
        if not self.fonts: return None
        font_params = []
        for font_name, weights in sorted(self.fonts.items()):
            sorted_weights = sorted(list(set(weights)), key=int)
            weights_str = ';'.join(sorted_weights)
            font_params.append(f"family={quote(font_name)}:wght@{weights_str}")
        
        return f"https://fonts.googleapis.com/css2?{'&'.join(font_params)}&display=swap"

    def print_report(self):
        self.logger.subsection("字体分析报告")
        if not self.fonts:
            self.logger.warning("未找到任何可用的自定义字体。")
            return
        
        self.logger.info(f"在 {self.processed_files} 个文件中分析出 {len(self.fonts)} 个自定义字体:")
        for font, weights in sorted(self.fonts.items()):
            self.logger.info(f"  📝 {font}: Weights {', '.join(sorted(list(weights), key=int))}")
        
        google_url = self.generate_google_fonts_url()
        if google_url:
            self.logger.info(f"\n🔗 建议的 Google Fonts URL:")
            self.logger.info(google_url)

# --- 主处理器 ---
class CodePreprocessor:
    def __init__(self, logger, base_font_size=16):
        self.logger = logger
        self.project_dir = Path.cwd()
        self.image_downloader = ImageDownloader(logger)
        self.font_analyzer = FontAnalyzer(logger)
        self.px_to_rem_converter = PxToRemConverter(logger, base_font_size)

    def create_backup(self, file_path):
        """如果文件内容将要被修改，则创建备份。"""
        backup_path = file_path.with_suffix(file_path.suffix + '.bak')
        if backup_path.exists():
            self.logger.debug(f"备份已存在，跳过创建: {backup_path.name}")
            return
        try:
            shutil.copy2(file_path, backup_path)
            self.logger.info(f"已创建备份: {backup_path.name}")
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")

    def update_vite_config(self):
        """更新Vite配置以包含 base: '/'"""
        for config_name in ['vite.config.js', 'vite.config.ts']:
            vite_config_path = self.project_dir / config_name
            if vite_config_path.exists():
                break
        else:
            self.logger.warning("未找到 vite.config.js 或 vite.config.ts")
            return

        content = vite_config_path.read_text('utf-8')
        if "base:" in content:
            self.logger.info("Vite配置已包含base设置，跳过。")
            return

        new_content = re.sub(r'(defineConfig\s*\(\s*\{)', r"\1\n  base: '/',", content, 1)
        if new_content != content:
            self.create_backup(vite_config_path)
            vite_config_path.write_text(new_content, 'utf-8')
            self.logger.success("已更新Vite配置，添加了 `base: '/'`。")
        else:
            self.logger.error("无法自动向Vite配置中添加 `base`。")

    def update_html_file(self, html_path):
        """更新HTML文件，添加字体预连接和样式表。"""
        if not html_path.exists():
            self.logger.error(f"HTML文件不存在: {html_path}")
            return
        
        self.create_backup(html_path)
        content = html_path.read_text('utf-8')
        soup = BeautifulSoup(content, 'html.parser')
        head = soup.find('head')
        if not head:
            self.logger.error("在HTML文件中未找到<head>标签。")
            return
        
        updated = False
        # 添加 Preconnect
        preconnect_urls = {
            'https://fonts.googleapis.com': False,
            'https://fonts.gstatic.com': True  # has crossorigin
        }
        for link in head.find_all('link', rel='preconnect'):
            href = link.get('href')
            if href in preconnect_urls:
                del preconnect_urls[href]

        for url, crossorigin in preconnect_urls.items():
            tag_attrs = {'rel': 'preconnect', 'href': url}
            if crossorigin:
                tag_attrs['crossorigin'] = True
            head.append(soup.new_tag('link', **tag_attrs))
            self.logger.success(f"添加了 preconnect 链接: {url}")
            updated = True
            
        # 添加 Google Fonts 样式表
        google_fonts_url = self.font_analyzer.generate_google_fonts_url()
        if google_fonts_url:
            if not any('fonts.googleapis.com' in link.get('href', '') for link in head.find_all('link', rel='stylesheet')):
                head.append(soup.new_tag('link', href=google_fonts_url, rel='stylesheet'))
                self.logger.success("添加了Google Fonts样式表。")
                updated = True

        if updated:
            html_path.write_text(str(soup.prettify()), 'utf-8')
            self.logger.success(f"HTML文件已更新: {html_path.name}")
        else:
            self.logger.info(f"HTML文件无需更新: {html_path.name}")

    def run(self, **kwargs):
        """运行主处理流程"""
        self.logger.section("代码预处理开始")
        self.logger.info(f"项目目录: {self.project_dir}")
        
        src_dir = self.project_dir / kwargs.get('src_dir', 'src')
        public_dir = self.project_dir / kwargs.get('public_dir', 'public')
        html_file = self.project_dir / kwargs.get('html_file', 'index.html')
        
        process_images = kwargs.get('process_images', True)
        process_fonts = kwargs.get('process_fonts', True)
        process_px_to_rem = kwargs.get('process_px_to_rem', True)
        update_config = kwargs.get('update_config', True)

        if not src_dir.exists():
            self.logger.error(f"源目录不存在: {src_dir}")
            return False

        public_images_dir = public_dir / 'images'
        if process_images:
            public_images_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"确保图片目录存在: {public_images_dir}")

        source_exts = ('.jsx', '.js', '.tsx', '.ts', '.vue', '.svelte')
        style_exts = ('.css', '.scss', '.sass', '.less')

        self.logger.subsection("第一阶段: 分析与内容转换")
        for file_path in src_dir.rglob('*'):
            if not file_path.is_file(): continue

            is_style = file_path.suffix in style_exts
            is_source = file_path.suffix in source_exts

            if not (is_style or is_source): continue
            
            try:
                self.logger.info(f"正在处理: {file_path.relative_to(self.project_dir)}")
                original_content = file_path.read_text('utf-8')
                content_to_modify = original_content
                
                # [优化] 跟踪每个操作是否导致了修改
                modifications = []

                if process_px_to_rem and is_style:
                    new_content = self.px_to_rem_converter.process_content(content_to_modify, file_path)
                    if new_content != content_to_modify:
                        content_to_modify = new_content
                        modifications.append("px->rem转换")

                if process_images and (is_style or is_source):
                    new_content = self.image_downloader.process_content(content_to_modify, public_images_dir)
                    if new_content != content_to_modify:
                        content_to_modify = new_content
                        modifications.append("图片链接替换")
                
                if process_fonts and is_style:
                    self.font_analyzer.analyze_content(original_content, file_path)
                
                if content_to_modify != original_content:
                    # [优化] 日志更清晰，说明修改原因
                    self.logger.success(f"文件内容已更新: {file_path.name} (操作: {', '.join(modifications)})")
                    self.create_backup(file_path)
                    file_path.write_text(content_to_modify, 'utf-8')
                else:
                    # [优化] 对于未修改的文件也提供反馈
                    self.logger.debug(f"文件无需修改: {file_path.name}")

            except Exception as e:
                self.logger.error(f"处理文件失败 {file_path}: {e}")
        
        if update_config:
            self.logger.subsection("第二阶段: 更新项目配置")
            if process_images: self.update_vite_config()
            if process_fonts: self.update_html_file(html_file)

        self.logger.section("处理完成 - 最终报告")
        if process_px_to_rem: self.px_to_rem_converter.print_report()
        if process_images: self.logger.info(self.image_downloader.get_stats_summary())
        if process_fonts: self.font_analyzer.print_report()
        
        self.logger.success(f"\n✅ 所有操作完成！总耗时: {self.logger.get_elapsed_time()}")
        self.logger.info("💡 提示: 请检查修改并重启开发服务器以应用所有更改。")
        return True

def main():
    parser = argparse.ArgumentParser(
        description='代码预处理工具 - 自动处理图片本地化、字体分析和px转rem。',
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""
示例用法:
  # 运行所有功能 (推荐)
  python %(prog)s
  
  # 仅处理图片
  python %(prog)s --images-only
  
  # 仅执行px转rem转换
  python %(prog)s --px-to-rem-only --base-font-size 10
  
  # 禁用px转rem，并指定自定义项目结构
  python %(prog)s --no-px-to-rem --src-dir app --public-dir static --html-file static/index.html
  
  # 静默运行并保存日志
  python %(prog)s --quiet --log preprocessor.log
"""
    )
    
    group = parser.add_argument_group('功能控制')
    group.add_argument('--images-only', action='store_true', help='仅处理图片下载。')
    group.add_argument('--fonts-only', action='store_true', help='仅分析字体并更新HTML。')
    group.add_argument('--px-to-rem-only', action='store_true', help='仅执行px转rem转换。')
    group.add_argument('--no-px-to-rem', action='store_true', help='禁用px转rem转换。')
    group.add_argument('--no-config', action='store_true', help='不更新Vite和HTML配置文件。')
    
    path_group = parser.add_argument_group('路径配置')
    path_group.add_argument('--src-dir', default='src', help='源文件目录 (默认: src)')
    path_group.add_argument('--public-dir', default='public', help='公共资源目录 (默认: public)')
    path_group.add_argument('--html-file', default='index.html', help='主HTML文件路径 (默认: index.html)')
    
    other_group = parser.add_argument_group('其他选项')
    other_group.add_argument('--base-font-size', type=int, default=16, help='px转rem的基准字体大小 (默认: 16)')
    other_group.add_argument('--log', type=str, help='将日志输出到指定文件。')
    other_group.add_argument('--quiet', action='store_true', help='静默模式，控制台仅输出错误。')
    
    args = parser.parse_args()
    
    # 确定处理选项
    if args.images_only:
        run_args = {'process_images': True, 'process_fonts': False, 'process_px_to_rem': False}
    elif args.fonts_only:
        run_args = {'process_images': False, 'process_fonts': True, 'process_px_to_rem': False}
    elif args.px_to_rem_only:
        run_args = {'process_images': False, 'process_fonts': False, 'process_px_to_rem': True}
    else:
        run_args = {'process_images': True, 'process_fonts': True, 'process_px_to_rem': not args.no_px_to_rem}
    
    run_args['update_config'] = not args.no_config
    run_args['src_dir'] = args.src_dir
    run_args['public_dir'] = args.public_dir
    run_args['html_file'] = args.html_file

    global logger
    logger = Logger(log_file=args.log, verbose=not args.quiet)
    
    preprocessor = CodePreprocessor(logger, args.base_font_size)
    success = preprocessor.run(**run_args)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())