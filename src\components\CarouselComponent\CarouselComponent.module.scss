.carouselContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.carouselWrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.carouselTrack {
  display: flex;
  align-items: flex-start;
  height: 100%;
  will-change: transform;
}

.carouselItem {
  flex-shrink: 0;
  height: 100%;
  display: flex;
  align-items: flex-start;
  border-radius: 0.75rem 0.75rem 0rem 0rem;
  overflow: hidden;
}

.carouselImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

// 响应式设计
@media (max-width: 768px) {
  .carouselContainer {
    // 移动端可以调整轮播速度或其他属性
  }
}
